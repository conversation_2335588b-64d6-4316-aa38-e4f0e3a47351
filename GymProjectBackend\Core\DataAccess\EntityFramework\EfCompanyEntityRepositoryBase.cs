using Core.Entities;
using Core.Utilities.Security.CompanyContext;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

namespace Core.DataAccess.EntityFramework
{
    public class EfCompanyEntityRepositoryBase<TEntity, TContext> : EfEntityRepositoryBase<TEntity, TContext>, IEntityRepository<TEntity>
        where TEntity : class, ICompanyEntity, new()
        where TContext : DbContext
    {
        private readonly ICompanyContext _companyContext;

        public EfCompanyEntityRepositoryBase(TContext context, ICompanyContext companyContext) : base(context)
        {
            _companyContext = companyContext;
        }

        public new List<TEntity> GetAll(Expression<Func<TEntity, bool>> filter = null)
        {
            // Mevcut kullanıcının şirket ID'sini al
            int companyId = _companyContext.GetCompanyId();

            // Eğer şirket ID'si geçerli değilse, boş liste döndür
            if (companyId <= 0)
            {
                return new List<TEntity>();
            }

            // Şirket ID'sine göre filtrele
            var query = _context.Set<TEntity>().Where(e => e.CompanyID == companyId);

            // Eğer ek filtre varsa, uygula
            if (filter != null)
            {
                query = query.Where(filter);
            }

            return query.ToList();
        }

        public new TEntity Get(Expression<Func<TEntity, bool>> filter)
        {
            // Mevcut kullanıcının şirket ID'sini al
            int companyId = _companyContext.GetCompanyId();

            // Eğer şirket ID'si geçerli değilse, null döndür
            if (companyId <= 0)
            {
                return null;
            }

            // Şirket ID'sine göre filtrele ve ek filtreyi uygula
            return _context.Set<TEntity>()
                .Where(e => e.CompanyID == companyId)
                .SingleOrDefault(filter);
        }

        public new void Add(TEntity entity)
        {
            // Mevcut kullanıcının şirket ID'sini al
            int companyId = _companyContext.GetCompanyId();

            // Eğer şirket ID'si geçerli değilse, işlemi iptal et
            if (companyId <= 0)
            {
                throw new Exception("Geçerli bir şirket ID'si bulunamadı.");
            }

            // Varlığın şirket ID'sini ayarla
            entity.CompanyID = companyId;

            // Temel sınıfın Add metodunu çağır
            base.Add(entity);
        }

        public new void Update(TEntity entity)
        {
            // Mevcut kullanıcının şirket ID'sini al
            int companyId = _companyContext.GetCompanyId();

            // Eğer şirket ID'si geçerli değilse, işlemi iptal et
            if (companyId <= 0)
            {
                throw new Exception("Geçerli bir şirket ID'si bulunamadı.");
            }

            // Varlığın şirket ID'sini ayarla
            entity.CompanyID = companyId;

            // Temel sınıfın Update metodunu çağır
            base.Update(entity);
        }
    }
}
