using Core.DataAccess.EntityFramework;
using Core.Extensions;
using Core.Utilities.Paging;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfSystemExerciseDal : EfEntityRepositoryBase<SystemExercise, GymContext>, ISystemExerciseDal
    {
        public EfSystemExerciseDal(GymContext context) : base(context)
        {
        }

        public List<SystemExerciseDto> GetAllSystemExercises()
        {
            var result = from se in _context.SystemExercises
                             join ec in _context.ExerciseCategories on se.ExerciseCategoryID equals ec.ExerciseCategoryID
                             where se.IsActive == true && ec.IsActive == true
                             orderby ec.CategoryName, se.ExerciseName
                             select new SystemExerciseDto
                             {
                                 SystemExerciseID = se.SystemExerciseID,
                                 ExerciseCategoryID = se.ExerciseCategoryID,
                                 CategoryName = ec.CategoryName,
                                 ExerciseName = se.ExerciseName,
                                 Description = se.Description,
                                 Instructions = se.Instructions,
                                 MuscleGroups = se.MuscleGroups,
                                 Equipment = se.Equipment,
                                 DifficultyLevel = se.DifficultyLevel,
                                 DifficultyLevelText = se.DifficultyLevel == 1 ? "Başlangıç" : 
                                                      se.DifficultyLevel == 2 ? "Orta" : 
                                                      se.DifficultyLevel == 3 ? "İleri" : "",
                                 IsActive = se.IsActive,
                                 CreationDate = se.CreationDate
                             };
                return result.ToList();
        }

        public List<SystemExerciseDto> GetSystemExercisesByCategory(int categoryId)
        {
            var result = from se in _context.SystemExercises
                         join ec in _context.ExerciseCategories on se.ExerciseCategoryID equals ec.ExerciseCategoryID
                             where se.ExerciseCategoryID == categoryId && se.IsActive == true && ec.IsActive == true
                             orderby se.ExerciseName
                             select new SystemExerciseDto
                             {
                                 SystemExerciseID = se.SystemExerciseID,
                                 ExerciseCategoryID = se.ExerciseCategoryID,
                                 CategoryName = ec.CategoryName,
                                 ExerciseName = se.ExerciseName,
                                 Description = se.Description,
                                 Instructions = se.Instructions,
                                 MuscleGroups = se.MuscleGroups,
                                 Equipment = se.Equipment,
                                 DifficultyLevel = se.DifficultyLevel,
                                 DifficultyLevelText = se.DifficultyLevel == 1 ? "Başlangıç" : 
                                                      se.DifficultyLevel == 2 ? "Orta" : 
                                                      se.DifficultyLevel == 3 ? "İleri" : "",
                                 IsActive = se.IsActive,
                                 CreationDate = se.CreationDate
                             };
                return result.ToList();
        }

        public PaginatedResult<SystemExerciseDto> GetSystemExercisesFiltered(SystemExerciseFilterDto filter)
        {
            using (GymContext context = new GymContext())
            {
                var query = from se in context.SystemExercises
                           join ec in context.ExerciseCategories on se.ExerciseCategoryID equals ec.ExerciseCategoryID
                           where se.IsActive == true && ec.IsActive == true
                           select new SystemExerciseDto
                           {
                               SystemExerciseID = se.SystemExerciseID,
                               ExerciseCategoryID = se.ExerciseCategoryID,
                               CategoryName = ec.CategoryName,
                               ExerciseName = se.ExerciseName,
                               Description = se.Description,
                               Instructions = se.Instructions,
                               MuscleGroups = se.MuscleGroups,
                               Equipment = se.Equipment,
                               DifficultyLevel = se.DifficultyLevel,
                               DifficultyLevelText = se.DifficultyLevel == 1 ? "Başlangıç" : 
                                                    se.DifficultyLevel == 2 ? "Orta" : 
                                                    se.DifficultyLevel == 3 ? "İleri" : "",
                               IsActive = se.IsActive,
                               CreationDate = se.CreationDate
                           };

                // Filtreleme
                if (filter.ExerciseCategoryID.HasValue)
                {
                    query = query.Where(x => x.ExerciseCategoryID == filter.ExerciseCategoryID.Value);
                }

                if (!string.IsNullOrWhiteSpace(filter.SearchTerm))
                {
                    var searchTerm = filter.SearchTerm.ToLower();
                    query = query.Where(x =>
                        (x.ExerciseName != null && x.ExerciseName.ToLower().Contains(searchTerm)) ||
                        (x.Description != null && x.Description.ToLower().Contains(searchTerm)) ||
                        (x.MuscleGroups != null && x.MuscleGroups.ToLower().Contains(searchTerm)));
                }

                if (filter.DifficultyLevel.HasValue)
                {
                    query = query.Where(x => x.DifficultyLevel == filter.DifficultyLevel.Value);
                }

                if (!string.IsNullOrWhiteSpace(filter.Equipment))
                {
                    var equipment = filter.Equipment.ToLower();
                    query = query.Where(x => x.Equipment != null && x.Equipment.ToLower().Contains(equipment));
                }

                // Sıralama
                query = query.OrderBy(x => x.CategoryName).ThenBy(x => x.ExerciseName);

                return query.ToPaginatedResult(filter.Page, filter.PageSize);
            }
        }

        public List<SystemExerciseDto> SearchSystemExercises(string searchTerm)
        {
            using (GymContext context = new GymContext())
            {
                // Null kontrolü ekle
                if (string.IsNullOrWhiteSpace(searchTerm))
                {
                    return new List<SystemExerciseDto>();
                }

                var result = from se in context.SystemExercises
                             join ec in context.ExerciseCategories on se.ExerciseCategoryID equals ec.ExerciseCategoryID
                             where se.IsActive == true && ec.IsActive == true &&
                                   (se.ExerciseName.Contains(searchTerm) ||
                                    (se.Description != null && se.Description.Contains(searchTerm)) ||
                                    (se.MuscleGroups != null && se.MuscleGroups.Contains(searchTerm)) ||
                                    ec.CategoryName.Contains(searchTerm))
                             orderby se.ExerciseName
                             select new SystemExerciseDto
                             {
                                 SystemExerciseID = se.SystemExerciseID,
                                 ExerciseCategoryID = se.ExerciseCategoryID,
                                 CategoryName = ec.CategoryName,
                                 ExerciseName = se.ExerciseName,
                                 Description = se.Description,
                                 Instructions = se.Instructions,
                                 MuscleGroups = se.MuscleGroups,
                                 Equipment = se.Equipment,
                                 DifficultyLevel = se.DifficultyLevel,
                                 DifficultyLevelText = se.DifficultyLevel == 1 ? "Başlangıç" :
                                                      se.DifficultyLevel == 2 ? "Orta" :
                                                      se.DifficultyLevel == 3 ? "İleri" : "",
                                 IsActive = se.IsActive,
                                 CreationDate = se.CreationDate
                             };
                return result.ToList();
            }
        }

        public SystemExerciseDto GetSystemExerciseDetail(int exerciseId)
        {
            var result = from se in _context.SystemExercises
                             join ec in _context.ExerciseCategories on se.ExerciseCategoryID equals ec.ExerciseCategoryID
                             where se.SystemExerciseID == exerciseId && se.IsActive == true
                             select new SystemExerciseDto
                             {
                                 SystemExerciseID = se.SystemExerciseID,
                                 ExerciseCategoryID = se.ExerciseCategoryID,
                                 CategoryName = ec.CategoryName,
                                 ExerciseName = se.ExerciseName,
                                 Description = se.Description,
                                 Instructions = se.Instructions,
                                 MuscleGroups = se.MuscleGroups,
                                 Equipment = se.Equipment,
                                 DifficultyLevel = se.DifficultyLevel,
                                 DifficultyLevelText = se.DifficultyLevel == 1 ? "Başlangıç" : 
                                                      se.DifficultyLevel == 2 ? "Orta" : 
                                                      se.DifficultyLevel == 3 ? "İleri" : "",
                                 IsActive = se.IsActive,
                                 CreationDate = se.CreationDate
                             };
                return result.FirstOrDefault();
        }
    }
}
