﻿using Core.DataAccess.EntityFramework;
using Core.Utilities.Paging;
using DataAccess.Abstract;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfProductDal : EfCompanyEntityRepositoryBase<Product, GymContext>, IProductDal
    {
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;

        public EfProductDal(GymContext context, Core.Utilities.Security.CompanyContext.ICompanyContext companyContext) : base(context, companyContext)
        {
            _companyContext = companyContext;
        }

        public PaginatedResult<Product> GetAllPaginated(ProductPagingParameters parameters)
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var query = context.Products.Where(x => x.IsActive == true && x.CompanyID == companyId);

                // Filtreleme
                if (!string.IsNullOrWhiteSpace(parameters.SearchText))
                {
                    query = query.Where(x => x.Name.Contains(parameters.SearchText));
                }

                if (parameters.MinPrice.HasValue)
                {
                    query = query.Where(x => x.Price >= parameters.MinPrice.Value);
                }

                if (parameters.MaxPrice.HasValue)
                {
                    query = query.Where(x => x.Price <= parameters.MaxPrice.Value);
                }

                if (parameters.IsActive.HasValue)
                {
                    query = query.Where(x => x.IsActive == parameters.IsActive.Value);
                }

                // Sıralama
                switch (parameters.SortBy?.ToLower())
                {
                    case "name":
                        query = parameters.SortDirection?.ToLower() == "asc"
                            ? query.OrderBy(x => x.Name)
                            : query.OrderByDescending(x => x.Name);
                        break;
                    case "price":
                        query = parameters.SortDirection?.ToLower() == "asc"
                            ? query.OrderBy(x => x.Price)
                            : query.OrderByDescending(x => x.Price);
                        break;
                    case "creationdate":
                        query = parameters.SortDirection?.ToLower() == "asc"
                            ? query.OrderBy(x => x.CreationDate)
                            : query.OrderByDescending(x => x.CreationDate);
                        break;
                    default: // ProductID
                        query = parameters.SortDirection?.ToLower() == "asc"
                            ? query.OrderBy(x => x.ProductID)
                            : query.OrderByDescending(x => x.ProductID);
                        break;
                }

                // Sayfalama için toplam kayıt sayısını al
                var totalCount = query.Count();

                // Sayfalama uygula
                var items = query
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .ToList();

                return new PaginatedResult<Product>(items, parameters.PageNumber, parameters.PageSize, totalCount);
            }
        }
    }
}
