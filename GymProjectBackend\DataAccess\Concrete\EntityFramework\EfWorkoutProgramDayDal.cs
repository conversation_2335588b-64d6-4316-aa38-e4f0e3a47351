using Core.DataAccess.EntityFramework;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfWorkoutProgramDayDal : EfCompanyEntityRepositoryBase<WorkoutProgramDay, GymContext>, IWorkoutProgramDayDal
    {
        public EfWorkoutProgramDayDal(GymContext context, ICompanyContext companyContext) : base(context, companyContext)
        {
        }
    }
}
